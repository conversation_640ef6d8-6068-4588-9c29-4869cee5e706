<?php

namespace App\Http\Requests\Inventory;

use Illuminate\Foundation\Http\FormRequest;

class StoreInventoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'sku' => 'required|string|max:100|unique:products,sku',
            'barcode' => 'nullable|string|max:100|unique:products,barcode',
            'category_id' => 'nullable|integer|exists:categories,id',
            'supplier_id' => 'nullable|integer|exists:suppliers,id',
            'unit_price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'quantity_in_stock' => 'nullable|integer|min:0',
            'minimum_stock_level' => 'nullable|integer|min:0',
            'maximum_stock_level' => 'nullable|integer|min:0',
            'unit_of_measure' => 'nullable|string|max:50',
            'status' => 'nullable|in:active,inactive,discontinued',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => __('inventory.name_required'),
            'name.max' => __('inventory.name_max'),
            'description.max' => __('inventory.description_max'),
            'sku.required' => __('inventory.sku_required'),
            'sku.max' => __('inventory.sku_max'),
            'sku.unique' => __('inventory.sku_unique'),
            'barcode.max' => __('inventory.barcode_max'),
            'barcode.unique' => __('inventory.barcode_unique'),
            'category_id.exists' => __('inventory.category_not_found'),
            'supplier_id.exists' => __('inventory.supplier_not_found'),
            'unit_price.required' => __('inventory.unit_price_required'),
            'unit_price.numeric' => __('inventory.unit_price_numeric'),
            'unit_price.min' => __('inventory.unit_price_min'),
            'cost_price.numeric' => __('inventory.cost_price_numeric'),
            'cost_price.min' => __('inventory.cost_price_min'),
            'quantity_in_stock.integer' => __('inventory.quantity_integer'),
            'quantity_in_stock.min' => __('inventory.quantity_min'),
            'minimum_stock_level.integer' => __('inventory.minimum_stock_integer'),
            'minimum_stock_level.min' => __('inventory.minimum_stock_min'),
            'maximum_stock_level.integer' => __('inventory.maximum_stock_integer'),
            'maximum_stock_level.min' => __('inventory.maximum_stock_min'),
            'unit_of_measure.max' => __('inventory.unit_of_measure_max'),
            'status.in' => __('inventory.status_invalid'),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'name' => __('inventory.name'),
            'description' => __('inventory.description'),
            'sku' => __('inventory.sku'),
            'barcode' => __('inventory.barcode'),
            'category_id' => __('inventory.category'),
            'supplier_id' => __('inventory.supplier'),
            'unit_price' => __('inventory.unit_price'),
            'cost_price' => __('inventory.cost_price'),
            'quantity_in_stock' => __('inventory.quantity_in_stock'),
            'minimum_stock_level' => __('inventory.minimum_stock_level'),
            'maximum_stock_level' => __('inventory.maximum_stock_level'),
            'unit_of_measure' => __('inventory.unit_of_measure'),
            'status' => __('inventory.status'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values if not provided
        $defaults = [
            'quantity_in_stock' => 0,
            'minimum_stock_level' => 0,
            'unit_of_measure' => 'piece',
            'status' => 'active',
        ];

        foreach ($defaults as $key => $value) {
            if (!$this->has($key)) {
                $this->merge([$key => $value]);
            }
        }

        // Generate SKU if not provided
        if (!$this->has('sku') || empty($this->input('sku'))) {
            $this->merge([
                'sku' => 'SKU-' . strtoupper(uniqid()),
            ]);
        }
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate that maximum stock level is greater than minimum stock level
            if ($this->filled('minimum_stock_level') && $this->filled('maximum_stock_level')) {
                if ($this->input('maximum_stock_level') <= $this->input('minimum_stock_level')) {
                    $validator->errors()->add('maximum_stock_level', __('inventory.maximum_stock_greater_than_minimum'));
                }
            }
        });
    }
}
