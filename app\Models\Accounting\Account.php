<?php

namespace App\Models\Accounting;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Account extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'accounts';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'type',
        'description',
        'balance',
        'currency',
        'bank_name',
        'account_number',
        'routing_number',
        'status',
        'is_default',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'balance' => 'decimal:2',
        'is_default' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the transactions for the account.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Scope a query to only include accounts of a specific type.
     */
    public function scopeType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include active accounts.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include asset accounts.
     */
    public function scopeAssets($query)
    {
        return $query->where('type', 'asset');
    }

    /**
     * Scope a query to only include liability accounts.
     */
    public function scopeLiabilities($query)
    {
        return $query->where('type', 'liability');
    }

    /**
     * Scope a query to only include equity accounts.
     */
    public function scopeEquity($query)
    {
        return $query->where('type', 'equity');
    }

    /**
     * Scope a query to only include revenue accounts.
     */
    public function scopeRevenue($query)
    {
        return $query->where('type', 'revenue');
    }

    /**
     * Scope a query to only include expense accounts.
     */
    public function scopeExpense($query)
    {
        return $query->where('type', 'expense');
    }

    /**
     * Get the current balance of the account.
     */
    public function getCurrentBalance(): float
    {
        return $this->balance;
    }

    /**
     * Get the calculated balance based on transactions.
     */
    public function getCalculatedBalance(): float
    {
        $income = $this->transactions()
            ->where('type', 'income')
            ->where('status', 'completed')
            ->sum('amount');

        $expenses = $this->transactions()
            ->where('type', 'expense')
            ->where('status', 'completed')
            ->sum('amount');

        return $income - $expenses;
    }

    /**
     * Get the balance for a specific date.
     */
    public function getBalanceAsOf(string $date): float
    {
        $income = $this->transactions()
            ->where('type', 'income')
            ->where('status', 'completed')
            ->where('transaction_date', '<=', $date)
            ->sum('amount');

        $expenses = $this->transactions()
            ->where('type', 'expense')
            ->where('status', 'completed')
            ->where('transaction_date', '<=', $date)
            ->sum('amount');

        return $income - $expenses;
    }

    /**
     * Get the total income for this account.
     */
    public function getTotalIncome(): float
    {
        return $this->transactions()
            ->where('type', 'income')
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Get the total expenses for this account.
     */
    public function getTotalExpenses(): float
    {
        return $this->transactions()
            ->where('type', 'expense')
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Check if this is a bank account.
     */
    public function isBankAccount(): bool
    {
        return !empty($this->account_number) || !empty($this->bank_name);
    }

    /**
     * Check if this is the default account.
     */
    public function isDefault(): bool
    {
        return $this->is_default;
    }

    /**
     * Get the formatted balance with currency symbol.
     */
    public function getFormattedBalanceAttribute(): string
    {
        $symbol = $this->currency === 'USD' ? '$' : $this->currency;
        return $symbol . ' ' . number_format($this->balance, 2);
    }

    /**
     * Get the masked account number for display.
     */
    public function getMaskedAccountNumberAttribute(): string
    {
        if (empty($this->account_number)) {
            return '';
        }

        $length = strlen($this->account_number);
        if ($length <= 4) {
            return $this->account_number;
        }

        return str_repeat('*', $length - 4) . substr($this->account_number, -4);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($account) {
            if (empty($account->code)) {
                $account->code = strtoupper(substr($account->name, 0, 3)) . '-' . rand(1000, 9999);
            }
            
            if (empty($account->currency)) {
                $account->currency = 'USD';
            }
        });
    }
}
