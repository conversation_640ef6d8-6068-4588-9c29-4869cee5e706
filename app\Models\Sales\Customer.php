<?php

namespace App\Models\Sales;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'tax_number',
        'customer_type',
        'credit_limit',
        'payment_terms',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'credit_limit' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the sales for the customer.
     */
    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Scope a query to only include active customers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include customers of a specific type.
     */
    public function scopeType($query, string $type)
    {
        return $query->where('customer_type', $type);
    }

    /**
     * Get the total sales amount for this customer.
     */
    public function getTotalSalesAmount(): float
    {
        return $this->sales()->sum('net_amount');
    }

    /**
     * Get the total number of sales for this customer.
     */
    public function getTotalSalesCount(): int
    {
        return $this->sales()->count();
    }

    /**
     * Get the average sale amount for this customer.
     */
    public function getAverageSaleAmount(): float
    {
        $count = $this->getTotalSalesCount();
        return $count > 0 ? $this->getTotalSalesAmount() / $count : 0;
    }

    /**
     * Get the outstanding balance for this customer.
     */
    public function getOutstandingBalance(): float
    {
        return $this->sales()
            ->whereIn('payment_status', ['unpaid', 'partially_paid'])
            ->sum('net_amount');
    }

    /**
     * Check if customer has exceeded credit limit.
     */
    public function hasExceededCreditLimit(): bool
    {
        if (!$this->credit_limit) {
            return false;
        }

        return $this->getOutstandingBalance() > $this->credit_limit;
    }

    /**
     * Get the full address as a string.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }
}
