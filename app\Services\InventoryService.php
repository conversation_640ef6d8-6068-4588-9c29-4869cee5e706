<?php

namespace App\Services;

use App\Models\Inventory\Product;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class InventoryService
{
    /**
     * Get all inventory items with pagination.
     *
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllItems(int $perPage = 15): LengthAwarePaginator
    {
        return Product::with(['category', 'supplier'])
            ->orderBy('name')
            ->paginate($perPage);
    }

    /**
     * Create a new inventory item.
     *
     * @param array $data
     * @return Product
     * @throws \Exception
     */
    public function createItem(array $data): Product
    {
        DB::beginTransaction();
        
        try {
            $product = Product::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'sku' => $data['sku'],
                'barcode' => $data['barcode'] ?? null,
                'category_id' => $data['category_id'] ?? null,
                'supplier_id' => $data['supplier_id'] ?? null,
                'unit_price' => $data['unit_price'],
                'cost_price' => $data['cost_price'] ?? 0,
                'quantity_in_stock' => $data['quantity_in_stock'] ?? 0,
                'minimum_stock_level' => $data['minimum_stock_level'] ?? 0,
                'maximum_stock_level' => $data['maximum_stock_level'] ?? null,
                'unit_of_measure' => $data['unit_of_measure'] ?? 'piece',
                'status' => $data['status'] ?? 'active',
            ]);

            DB::commit();
            
            return $product->load(['category', 'supplier']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get inventory item by ID.
     *
     * @param int $id
     * @return Product|null
     */
    public function getItemById(int $id): ?Product
    {
        return Product::with(['category', 'supplier', 'stockMovements'])->find($id);
    }

    /**
     * Update inventory item.
     *
     * @param int $id
     * @param array $data
     * @return Product|null
     * @throws \Exception
     */
    public function updateItem(int $id, array $data): ?Product
    {
        $product = Product::find($id);
        
        if (!$product) {
            return null;
        }

        DB::beginTransaction();
        
        try {
            $product->update([
                'name' => $data['name'] ?? $product->name,
                'description' => $data['description'] ?? $product->description,
                'sku' => $data['sku'] ?? $product->sku,
                'barcode' => $data['barcode'] ?? $product->barcode,
                'category_id' => $data['category_id'] ?? $product->category_id,
                'supplier_id' => $data['supplier_id'] ?? $product->supplier_id,
                'unit_price' => $data['unit_price'] ?? $product->unit_price,
                'cost_price' => $data['cost_price'] ?? $product->cost_price,
                'minimum_stock_level' => $data['minimum_stock_level'] ?? $product->minimum_stock_level,
                'maximum_stock_level' => $data['maximum_stock_level'] ?? $product->maximum_stock_level,
                'unit_of_measure' => $data['unit_of_measure'] ?? $product->unit_of_measure,
                'status' => $data['status'] ?? $product->status,
            ]);

            DB::commit();
            
            return $product->load(['category', 'supplier']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete inventory item.
     *
     * @param int $id
     * @return bool
     */
    public function deleteItem(int $id): bool
    {
        $product = Product::find($id);
        
        if (!$product) {
            return false;
        }

        DB::beginTransaction();
        
        try {
            $product->stockMovements()->delete();
            $product->delete();
            
            DB::commit();
            
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update stock quantity.
     *
     * @param int $id
     * @param int $quantity
     * @param string $type (add, subtract, set)
     * @return Product|null
     * @throws \Exception
     */
    public function updateStock(int $id, int $quantity, string $type = 'set'): ?Product
    {
        $product = Product::find($id);
        
        if (!$product) {
            return null;
        }

        DB::beginTransaction();
        
        try {
            $oldQuantity = $product->quantity_in_stock;
            $newQuantity = $oldQuantity;

            switch ($type) {
                case 'add':
                    $newQuantity = $oldQuantity + $quantity;
                    break;
                case 'subtract':
                    $newQuantity = max(0, $oldQuantity - $quantity);
                    break;
                case 'set':
                    $newQuantity = max(0, $quantity);
                    break;
            }

            $product->update(['quantity_in_stock' => $newQuantity]);

            // Record stock movement
            $product->stockMovements()->create([
                'type' => $type,
                'quantity' => $quantity,
                'old_quantity' => $oldQuantity,
                'new_quantity' => $newQuantity,
                'reason' => 'Manual adjustment',
                'user_id' => auth()->id(),
            ]);

            DB::commit();
            
            return $product->load(['category', 'supplier']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get low stock items.
     *
     * @return Collection
     */
    public function getLowStockItems(): Collection
    {
        return Product::whereRaw('quantity_in_stock <= minimum_stock_level')
            ->where('status', 'active')
            ->with(['category', 'supplier'])
            ->orderBy('quantity_in_stock')
            ->get();
    }

    /**
     * Get inventory statistics.
     *
     * @return array
     */
    public function getInventoryStatistics(): array
    {
        $totalProducts = Product::where('status', 'active')->count();
        $totalValue = Product::where('status', 'active')
            ->selectRaw('SUM(quantity_in_stock * unit_price) as total')
            ->first()
            ->total ?? 0;
        
        $lowStockCount = Product::whereRaw('quantity_in_stock <= minimum_stock_level')
            ->where('status', 'active')
            ->count();
        
        $outOfStockCount = Product::where('quantity_in_stock', 0)
            ->where('status', 'active')
            ->count();

        return [
            'total_products' => $totalProducts,
            'total_value' => round($totalValue, 2),
            'low_stock_count' => $lowStockCount,
            'out_of_stock_count' => $outOfStockCount,
            'categories_count' => $this->getCategoriesCount(),
            'suppliers_count' => $this->getSuppliersCount(),
        ];
    }

    /**
     * Get categories count.
     *
     * @return int
     */
    private function getCategoriesCount(): int
    {
        return DB::table('categories')->count();
    }

    /**
     * Get suppliers count.
     *
     * @return int
     */
    private function getSuppliersCount(): int
    {
        return DB::table('suppliers')->count();
    }
}
