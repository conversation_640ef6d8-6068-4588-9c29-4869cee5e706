<?php

namespace App\Http\Requests\Sales;

use Illuminate\Foundation\Http\FormRequest;

class StoreSaleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_id' => 'required|integer|exists:customers,id',
            'sale_date' => 'nullable|date',
            'total_amount' => 'required|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'status' => 'nullable|in:pending,confirmed,shipped,delivered,cancelled',
            'notes' => 'nullable|string|max:1000',
            
            // Sale items validation
            'items' => 'nullable|array',
            'items.*.product_id' => 'required_with:items|integer|exists:products,id',
            'items.*.quantity' => 'required_with:items|integer|min:1',
            'items.*.unit_price' => 'required_with:items|numeric|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'customer_id.required' => __('sales.customer_required'),
            'customer_id.exists' => __('sales.customer_not_found'),
            'total_amount.required' => __('sales.total_amount_required'),
            'total_amount.numeric' => __('sales.total_amount_numeric'),
            'total_amount.min' => __('sales.total_amount_min'),
            'tax_amount.numeric' => __('sales.tax_amount_numeric'),
            'tax_amount.min' => __('sales.tax_amount_min'),
            'discount_amount.numeric' => __('sales.discount_amount_numeric'),
            'discount_amount.min' => __('sales.discount_amount_min'),
            'status.in' => __('sales.status_invalid'),
            'notes.max' => __('sales.notes_max'),
            
            // Items validation messages
            'items.array' => __('sales.items_array'),
            'items.*.product_id.required_with' => __('sales.item_product_required'),
            'items.*.product_id.exists' => __('sales.item_product_not_found'),
            'items.*.quantity.required_with' => __('sales.item_quantity_required'),
            'items.*.quantity.integer' => __('sales.item_quantity_integer'),
            'items.*.quantity.min' => __('sales.item_quantity_min'),
            'items.*.unit_price.required_with' => __('sales.item_price_required'),
            'items.*.unit_price.numeric' => __('sales.item_price_numeric'),
            'items.*.unit_price.min' => __('sales.item_price_min'),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'customer_id' => __('sales.customer'),
            'sale_date' => __('sales.sale_date'),
            'total_amount' => __('sales.total_amount'),
            'tax_amount' => __('sales.tax_amount'),
            'discount_amount' => __('sales.discount_amount'),
            'status' => __('sales.status'),
            'notes' => __('sales.notes'),
            'items' => __('sales.items'),
            'items.*.product_id' => __('sales.product'),
            'items.*.quantity' => __('sales.quantity'),
            'items.*.unit_price' => __('sales.unit_price'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default sale date if not provided
        if (!$this->has('sale_date')) {
            $this->merge([
                'sale_date' => now()->format('Y-m-d'),
            ]);
        }

        // Set default status if not provided
        if (!$this->has('status')) {
            $this->merge([
                'status' => 'pending',
            ]);
        }
    }
}
