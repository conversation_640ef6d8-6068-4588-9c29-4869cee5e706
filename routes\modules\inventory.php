<?php

use App\Http\Controllers\API\V1\Inventory\InventoryController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Inventory API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the Inventory module. These routes are loaded
| by the RouteServiceProvider and are assigned the "api" middleware group.
|
*/

Route::prefix('api/v1')->middleware(['api', 'auth:sanctum'])->group(function () {
    
    // Inventory Routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        
        // CRUD Operations
        Route::get('/', [InventoryController::class, 'index'])->name('index');
        Route::post('/', [InventoryController::class, 'store'])->name('store');
        Route::get('/{id}', [InventoryController::class, 'show'])->name('show');
        Route::put('/{id}', [InventoryController::class, 'update'])->name('update');
        Route::delete('/{id}', [InventoryController::class, 'destroy'])->name('destroy');
        
        // Stock Management Routes
        Route::put('/{id}/stock', [InventoryController::class, 'updateStock'])->name('update_stock');
        Route::get('/low-stock', [InventoryController::class, 'lowStock'])->name('low_stock');
        
    });
    
    // Products alias routes (alternative naming)
    Route::prefix('products')->name('products.')->group(function () {
        
        Route::get('/', [InventoryController::class, 'index'])->name('index');
        Route::post('/', [InventoryController::class, 'store'])->name('store');
        Route::get('/{id}', [InventoryController::class, 'show'])->name('show');
        Route::put('/{id}', [InventoryController::class, 'update'])->name('update');
        Route::delete('/{id}', [InventoryController::class, 'destroy'])->name('destroy');
        
    });
    
});

/*
|--------------------------------------------------------------------------
| Inventory Web Routes (if needed)
|--------------------------------------------------------------------------
*/

Route::prefix('inventory')->middleware(['web', 'auth'])->name('inventory.')->group(function () {
    
    // Web routes for inventory module can be added here
    // Example: Route::get('/', [WebInventoryController::class, 'index'])->name('web.index');
    
});
