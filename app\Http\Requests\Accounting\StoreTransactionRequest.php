<?php

namespace App\Http\Requests\Accounting;

use Illuminate\Foundation\Http\FormRequest;

class StoreTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'account_id' => 'required|integer|exists:accounts,id',
            'category_id' => 'nullable|integer|exists:transaction_categories,id',
            'type' => 'required|in:income,expense,transfer',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:500',
            'transaction_date' => 'nullable|date',
            'reference_number' => 'nullable|string|max:100',
            'status' => 'nullable|in:pending,completed,cancelled',
            
            // For transfer transactions
            'to_account_id' => 'required_if:type,transfer|integer|exists:accounts,id|different:account_id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'account_id.required' => __('accounting.account_required'),
            'account_id.exists' => __('accounting.account_not_found'),
            'category_id.exists' => __('accounting.category_not_found'),
            'type.required' => __('accounting.type_required'),
            'type.in' => __('accounting.type_invalid'),
            'amount.required' => __('accounting.amount_required'),
            'amount.numeric' => __('accounting.amount_numeric'),
            'amount.min' => __('accounting.amount_min'),
            'description.required' => __('accounting.description_required'),
            'description.max' => __('accounting.description_max'),
            'transaction_date.date' => __('accounting.transaction_date_invalid'),
            'reference_number.max' => __('accounting.reference_number_max'),
            'status.in' => __('accounting.status_invalid'),
            'to_account_id.required_if' => __('accounting.to_account_required_for_transfer'),
            'to_account_id.exists' => __('accounting.to_account_not_found'),
            'to_account_id.different' => __('accounting.to_account_different'),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'account_id' => __('accounting.account'),
            'category_id' => __('accounting.category'),
            'type' => __('accounting.type'),
            'amount' => __('accounting.amount'),
            'description' => __('accounting.description'),
            'transaction_date' => __('accounting.transaction_date'),
            'reference_number' => __('accounting.reference_number'),
            'status' => __('accounting.status'),
            'to_account_id' => __('accounting.to_account'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default transaction date if not provided
        if (!$this->has('transaction_date')) {
            $this->merge([
                'transaction_date' => now()->format('Y-m-d'),
            ]);
        }

        // Set default status if not provided
        if (!$this->has('status')) {
            $this->merge([
                'status' => 'completed',
            ]);
        }

        // Generate reference number if not provided
        if (!$this->has('reference_number') || empty($this->input('reference_number'))) {
            $this->merge([
                'reference_number' => 'TXN-' . strtoupper(uniqid()),
            ]);
        }
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Additional validation for transfer transactions
            if ($this->input('type') === 'transfer') {
                if ($this->input('account_id') === $this->input('to_account_id')) {
                    $validator->errors()->add('to_account_id', __('accounting.transfer_same_account_error'));
                }
            }

            // Validate amount precision (max 2 decimal places)
            if ($this->filled('amount')) {
                $amount = (string) $this->input('amount');
                if (strpos($amount, '.') !== false) {
                    $decimals = strlen(substr($amount, strpos($amount, '.') + 1));
                    if ($decimals > 2) {
                        $validator->errors()->add('amount', __('accounting.amount_precision_error'));
                    }
                }
            }
        });
    }
}
