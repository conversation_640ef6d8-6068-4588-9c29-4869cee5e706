<?php

namespace App\Http\Controllers\API\V1\Accounting;

use App\Http\Controllers\API\BaseApiController;
use App\Http\Requests\Accounting\StoreTransactionRequest;
use App\Http\Requests\Accounting\UpdateTransactionRequest;
use App\Services\AccountingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AccountingController extends BaseApiController
{
    protected AccountingService $accountingService;

    public function __construct(AccountingService $accountingService)
    {
        $this->accountingService = $accountingService;
    }

    /**
     * Display a listing of transactions.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $transactions = $this->accountingService->getAllTransactions($perPage);
            
            return $this->sendPaginatedResponse($transactions, __('accounting.retrieved_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('accounting.retrieval_failed'), [], 500);
        }
    }

    /**
     * Store a newly created transaction.
     *
     * @param StoreTransactionRequest $request
     * @return JsonResponse
     */
    public function store(StoreTransactionRequest $request): JsonResponse
    {
        try {
            $transaction = $this->accountingService->createTransaction($request->validated());
            
            return $this->sendResponse($transaction, __('accounting.created_successfully'), 201);
        } catch (\Exception $e) {
            return $this->sendError(__('accounting.creation_failed'), [], 500);
        }
    }

    /**
     * Display the specified transaction.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $transaction = $this->accountingService->getTransactionById($id);
            
            if (!$transaction) {
                return $this->sendError(__('accounting.not_found'), [], 404);
            }
            
            return $this->sendResponse($transaction, __('accounting.retrieved_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('accounting.retrieval_failed'), [], 500);
        }
    }

    /**
     * Update the specified transaction.
     *
     * @param UpdateTransactionRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateTransactionRequest $request, int $id): JsonResponse
    {
        try {
            $transaction = $this->accountingService->updateTransaction($id, $request->validated());
            
            if (!$transaction) {
                return $this->sendError(__('accounting.not_found'), [], 404);
            }
            
            return $this->sendResponse($transaction, __('accounting.updated_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('accounting.update_failed'), [], 500);
        }
    }

    /**
     * Remove the specified transaction.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $deleted = $this->accountingService->deleteTransaction($id);
            
            if (!$deleted) {
                return $this->sendError(__('accounting.not_found'), [], 404);
            }
            
            return $this->sendResponse(null, __('accounting.deleted_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('accounting.deletion_failed'), [], 500);
        }
    }

    /**
     * Get financial reports.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function reports(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'type' => 'required|in:income_statement,balance_sheet,cash_flow',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date'
            ]);

            $report = $this->accountingService->generateReport(
                $request->type,
                $request->start_date,
                $request->end_date
            );
            
            return $this->sendResponse($report, __('accounting.report_generated'));
        } catch (\Exception $e) {
            return $this->sendError(__('accounting.report_failed'), [], 500);
        }
    }

    /**
     * Get account balances.
     *
     * @return JsonResponse
     */
    public function balances(): JsonResponse
    {
        try {
            $balances = $this->accountingService->getAccountBalances();
            
            return $this->sendResponse($balances, __('accounting.balances_retrieved'));
        } catch (\Exception $e) {
            return $this->sendError(__('accounting.balances_failed'), [], 500);
        }
    }
}
