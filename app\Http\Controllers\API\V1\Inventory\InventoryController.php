<?php

namespace App\Http\Controllers\API\V1\Inventory;

use App\Http\Controllers\API\BaseApiController;
use App\Http\Requests\Inventory\StoreInventoryRequest;
use App\Http\Requests\Inventory\UpdateInventoryRequest;
use App\Services\InventoryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class InventoryController extends BaseApiController
{
    protected InventoryService $inventoryService;

    public function __construct(InventoryService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    /**
     * Display a listing of inventory items.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $items = $this->inventoryService->getAllItems($perPage);
            
            return $this->sendPaginatedResponse($items, __('inventory.retrieved_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('inventory.retrieval_failed'), [], 500);
        }
    }

    /**
     * Store a newly created inventory item.
     *
     * @param StoreInventoryRequest $request
     * @return JsonResponse
     */
    public function store(StoreInventoryRequest $request): JsonResponse
    {
        try {
            $item = $this->inventoryService->createItem($request->validated());
            
            return $this->sendResponse($item, __('inventory.created_successfully'), 201);
        } catch (\Exception $e) {
            return $this->sendError(__('inventory.creation_failed'), [], 500);
        }
    }

    /**
     * Display the specified inventory item.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $item = $this->inventoryService->getItemById($id);
            
            if (!$item) {
                return $this->sendError(__('inventory.not_found'), [], 404);
            }
            
            return $this->sendResponse($item, __('inventory.retrieved_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('inventory.retrieval_failed'), [], 500);
        }
    }

    /**
     * Update the specified inventory item.
     *
     * @param UpdateInventoryRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateInventoryRequest $request, int $id): JsonResponse
    {
        try {
            $item = $this->inventoryService->updateItem($id, $request->validated());
            
            if (!$item) {
                return $this->sendError(__('inventory.not_found'), [], 404);
            }
            
            return $this->sendResponse($item, __('inventory.updated_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('inventory.update_failed'), [], 500);
        }
    }

    /**
     * Remove the specified inventory item.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $deleted = $this->inventoryService->deleteItem($id);
            
            if (!$deleted) {
                return $this->sendError(__('inventory.not_found'), [], 404);
            }
            
            return $this->sendResponse(null, __('inventory.deleted_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('inventory.deletion_failed'), [], 500);
        }
    }

    /**
     * Update inventory stock.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateStock(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'quantity' => 'required|integer',
                'type' => 'required|in:add,subtract,set'
            ]);

            $item = $this->inventoryService->updateStock($id, $request->quantity, $request->type);
            
            if (!$item) {
                return $this->sendError(__('inventory.not_found'), [], 404);
            }
            
            return $this->sendResponse($item, __('inventory.stock_updated'));
        } catch (\Exception $e) {
            return $this->sendError(__('inventory.stock_update_failed'), [], 500);
        }
    }

    /**
     * Get low stock items.
     *
     * @return JsonResponse
     */
    public function lowStock(): JsonResponse
    {
        try {
            $items = $this->inventoryService->getLowStockItems();
            
            return $this->sendResponse($items, __('inventory.low_stock_retrieved'));
        } catch (\Exception $e) {
            return $this->sendError(__('inventory.low_stock_failed'), [], 500);
        }
    }
}
