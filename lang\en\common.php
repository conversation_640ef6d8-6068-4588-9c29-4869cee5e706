<?php

return [
    // General <PERSON>rms
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'loading' => 'Loading...',
    'saving' => 'Saving...',
    'deleting' => 'Deleting...',
    'processing' => 'Processing...',
    
    // Actions
    'create' => 'Create',
    'read' => 'Read',
    'update' => 'Update',
    'delete' => 'Delete',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'edit' => 'Edit',
    'view' => 'View',
    'search' => 'Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'import' => 'Import',
    'print' => 'Print',
    'download' => 'Download',
    'upload' => 'Upload',
    'submit' => 'Submit',
    'reset' => 'Reset',
    'refresh' => 'Refresh',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'close' => 'Close',
    'confirm' => 'Confirm',
    
    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',
    'enabled' => 'Enabled',
    'disabled' => 'Disabled',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'draft' => 'Draft',
    'published' => 'Published',
    
    // Data
    'name' => 'Name',
    'description' => 'Description',
    'date' => 'Date',
    'time' => 'Time',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'status' => 'Status',
    'type' => 'Type',
    'category' => 'Category',
    'amount' => 'Amount',
    'quantity' => 'Quantity',
    'price' => 'Price',
    'total' => 'Total',
    'subtotal' => 'Subtotal',
    'tax' => 'Tax',
    'discount' => 'Discount',
    'notes' => 'Notes',
    'reference' => 'Reference',
    'code' => 'Code',
    'id' => 'ID',
    
    // Messages
    'no_data' => 'No data available',
    'no_results' => 'No results found',
    'loading_data' => 'Loading data...',
    'data_saved' => 'Data saved successfully',
    'data_updated' => 'Data updated successfully',
    'data_deleted' => 'Data deleted successfully',
    'operation_successful' => 'Operation completed successfully',
    'operation_failed' => 'Operation failed',
    'access_denied' => 'Access denied',
    'unauthorized' => 'Unauthorized',
    'forbidden' => 'Forbidden',
    'not_found' => 'Not found',
    'validation_error' => 'Validation error',
    'server_error' => 'Server error',
    
    // Pagination
    'showing' => 'Showing',
    'to' => 'to',
    'of' => 'of',
    'results' => 'results',
    'per_page' => 'per page',
    'page' => 'Page',
    'first' => 'First',
    'last' => 'Last',
    
    // Confirmation
    'are_you_sure' => 'Are you sure?',
    'delete_confirmation' => 'Are you sure you want to delete this item?',
    'this_action_cannot_be_undone' => 'This action cannot be undone',
    'yes_delete' => 'Yes, delete it',
    'no_cancel' => 'No, cancel',
    
    // Time
    'today' => 'Today',
    'yesterday' => 'Yesterday',
    'tomorrow' => 'Tomorrow',
    'this_week' => 'This week',
    'last_week' => 'Last week',
    'this_month' => 'This month',
    'last_month' => 'Last month',
    'this_year' => 'This year',
    'last_year' => 'Last year',
    
    // Units
    'piece' => 'Piece',
    'pieces' => 'Pieces',
    'kg' => 'Kilogram',
    'gram' => 'Gram',
    'liter' => 'Liter',
    'meter' => 'Meter',
    'cm' => 'Centimeter',
    'box' => 'Box',
    'pack' => 'Pack',
    'bottle' => 'Bottle',
    'can' => 'Can',
];
