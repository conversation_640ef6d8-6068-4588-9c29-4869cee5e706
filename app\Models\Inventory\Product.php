<?php

namespace App\Models\Inventory;

use App\Models\Sales\SaleItem;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'products';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'sku',
        'barcode',
        'category_id',
        'supplier_id',
        'unit_price',
        'cost_price',
        'quantity_in_stock',
        'minimum_stock_level',
        'maximum_stock_level',
        'reorder_point',
        'reorder_quantity',
        'unit_of_measure',
        'weight',
        'dimensions',
        'location',
        'shelf',
        'bin',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'quantity_in_stock' => 'integer',
        'minimum_stock_level' => 'integer',
        'maximum_stock_level' => 'integer',
        'reorder_point' => 'integer',
        'reorder_quantity' => 'integer',
        'weight' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the category that owns the product.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the supplier that owns the product.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the stock movements for the product.
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    /**
     * Get the sale items for the product.
     */
    public function saleItems(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * Scope a query to only include active products.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include products with low stock.
     */
    public function scopeLowStock($query)
    {
        return $query->whereRaw('quantity_in_stock <= minimum_stock_level');
    }

    /**
     * Scope a query to only include out of stock products.
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('quantity_in_stock', 0);
    }

    /**
     * Scope a query to only include products in a specific category.
     */
    public function scopeInCategory($query, int $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope a query to only include products from a specific supplier.
     */
    public function scopeFromSupplier($query, int $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    /**
     * Check if the product is in stock.
     */
    public function isInStock(): bool
    {
        return $this->quantity_in_stock > 0;
    }

    /**
     * Check if the product has low stock.
     */
    public function hasLowStock(): bool
    {
        return $this->quantity_in_stock <= $this->minimum_stock_level;
    }

    /**
     * Check if the product is out of stock.
     */
    public function isOutOfStock(): bool
    {
        return $this->quantity_in_stock <= 0;
    }

    /**
     * Get the profit margin percentage.
     */
    public function getProfitMargin(): float
    {
        if ($this->cost_price <= 0) {
            return 0;
        }

        return (($this->unit_price - $this->cost_price) / $this->cost_price) * 100;
    }

    /**
     * Get the profit amount per unit.
     */
    public function getProfitPerUnit(): float
    {
        return max(0, $this->unit_price - $this->cost_price);
    }

    /**
     * Get the total inventory value for this product.
     */
    public function getInventoryValue(): float
    {
        return $this->quantity_in_stock * $this->cost_price;
    }

    /**
     * Get the total retail value for this product.
     */
    public function getRetailValue(): float
    {
        return $this->quantity_in_stock * $this->unit_price;
    }

    /**
     * Check if reorder is needed.
     */
    public function needsReorder(): bool
    {
        return $this->reorder_point > 0 && $this->quantity_in_stock <= $this->reorder_point;
    }

    /**
     * Get the suggested reorder quantity.
     */
    public function getSuggestedReorderQuantity(): int
    {
        if ($this->reorder_quantity > 0) {
            return $this->reorder_quantity;
        }

        if ($this->maximum_stock_level > 0) {
            return $this->maximum_stock_level - $this->quantity_in_stock;
        }

        return $this->minimum_stock_level * 2;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->sku)) {
                $product->sku = 'SKU-' . strtoupper(uniqid());
            }
        });
    }
}
