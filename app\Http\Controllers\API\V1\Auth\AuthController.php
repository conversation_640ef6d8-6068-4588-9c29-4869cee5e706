<?php

namespace App\Http\Controllers\API\V1\Auth;

use App\Http\Controllers\API\BaseApiController;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AuthController extends BaseApiController
{
    /**
     * Register a new user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function register(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);

            // Assign default role
            $user->assignRole('user');

            $token = $user->createToken('auth_token')->plainTextToken;

            $data = [
                'user' => $user,
                'access_token' => $token,
                'token_type' => 'Bearer',
            ];

            return $this->sendResponse($data, __('auth.registration_successful'), 201);
        } catch (\Exception $e) {
            return $this->sendError(__('auth.registration_failed'), [], 500);
        }
    }

    /**
     * Login user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors()->toArray());
        }

        try {
            if (!Auth::attempt($request->only('email', 'password'))) {
                return $this->sendError(__('auth.invalid_credentials'), [], 401);
            }

            $user = User::where('email', $request->email)->firstOrFail();
            $token = $user->createToken('auth_token')->plainTextToken;

            $data = [
                'user' => $user->load('roles'),
                'access_token' => $token,
                'token_type' => 'Bearer',
            ];

            return $this->sendResponse($data, __('auth.login_successful'));
        } catch (\Exception $e) {
            return $this->sendError(__('auth.login_failed'), [], 500);
        }
    }

    /**
     * Logout user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $request->user()->currentAccessToken()->delete();
            
            return $this->sendResponse(null, __('auth.logout_successful'));
        } catch (\Exception $e) {
            return $this->sendError(__('auth.logout_failed'), [], 500);
        }
    }

    /**
     * Get authenticated user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function user(Request $request): JsonResponse
    {
        try {
            $user = $request->user()->load('roles', 'permissions');
            
            return $this->sendResponse($user, __('auth.user_retrieved'));
        } catch (\Exception $e) {
            return $this->sendError(__('auth.user_retrieval_failed'), [], 500);
        }
    }

    /**
     * Refresh token.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function refresh(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $user->currentAccessToken()->delete();
            
            $token = $user->createToken('auth_token')->plainTextToken;

            $data = [
                'user' => $user->load('roles'),
                'access_token' => $token,
                'token_type' => 'Bearer',
            ];

            return $this->sendResponse($data, __('auth.token_refreshed'));
        } catch (\Exception $e) {
            return $this->sendError(__('auth.token_refresh_failed'), [], 500);
        }
    }
}
