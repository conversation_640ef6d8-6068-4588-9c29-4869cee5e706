<?php

use App\Http\Controllers\API\V1\Sales\SalesController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Sales API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the Sales module. These routes are loaded
| by the RouteServiceProvider and are assigned the "api" middleware group.
|
*/

Route::prefix('api/v1')->middleware(['api', 'auth:sanctum'])->group(function () {
    
    // Sales Routes
    Route::prefix('sales')->name('sales.')->group(function () {
        
        // CRUD Operations
        Route::get('/', [SalesController::class, 'index'])->name('index');
        Route::post('/', [SalesController::class, 'store'])->name('store');
        Route::get('/{id}', [SalesController::class, 'show'])->name('show');
        Route::put('/{id}', [SalesController::class, 'update'])->name('update');
        Route::delete('/{id}', [SalesController::class, 'destroy'])->name('destroy');
        
        // Additional Sales Routes
        Route::get('/statistics/overview', [SalesController::class, 'statistics'])->name('statistics');
        
    });
    
});

/*
|--------------------------------------------------------------------------
| Sales Web Routes (if needed)
|--------------------------------------------------------------------------
*/

Route::prefix('sales')->middleware(['web', 'auth'])->name('sales.')->group(function () {
    
    // Web routes for sales module can be added here
    // Example: Route::get('/', [WebSalesController::class, 'index'])->name('web.index');
    
});
