<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        then: function () {
            // Load module routes
            Route::middleware('api')
                ->group(base_path('routes/modules/auth.php'));

            Route::middleware('api')
                ->group(base_path('routes/modules/sales.php'));

            Route::middleware('api')
                ->group(base_path('routes/modules/inventory.php'));

            Route::middleware('api')
                ->group(base_path('routes/modules/accounting.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
