<?php

namespace App\Http\Controllers\API\V1\Sales;

use App\Http\Controllers\API\BaseApiController;
use App\Http\Requests\Sales\StoreSaleRequest;
use App\Http\Requests\Sales\UpdateSaleRequest;
use App\Services\SalesService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SalesController extends BaseApiController
{
    protected SalesService $salesService;

    public function __construct(SalesService $salesService)
    {
        $this->salesService = $salesService;
    }

    /**
     * Display a listing of sales.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $sales = $this->salesService->getAllSales($perPage);
            
            return $this->sendPaginatedResponse($sales, __('sales.retrieved_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('sales.retrieval_failed'), [], 500);
        }
    }

    /**
     * Store a newly created sale.
     *
     * @param StoreSaleRequest $request
     * @return JsonResponse
     */
    public function store(StoreSaleRequest $request): JsonResponse
    {
        try {
            $sale = $this->salesService->createSale($request->validated());
            
            return $this->sendResponse($sale, __('sales.created_successfully'), 201);
        } catch (\Exception $e) {
            return $this->sendError(__('sales.creation_failed'), [], 500);
        }
    }

    /**
     * Display the specified sale.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $sale = $this->salesService->getSaleById($id);
            
            if (!$sale) {
                return $this->sendError(__('sales.not_found'), [], 404);
            }
            
            return $this->sendResponse($sale, __('sales.retrieved_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('sales.retrieval_failed'), [], 500);
        }
    }

    /**
     * Update the specified sale.
     *
     * @param UpdateSaleRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateSaleRequest $request, int $id): JsonResponse
    {
        try {
            $sale = $this->salesService->updateSale($id, $request->validated());
            
            if (!$sale) {
                return $this->sendError(__('sales.not_found'), [], 404);
            }
            
            return $this->sendResponse($sale, __('sales.updated_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('sales.update_failed'), [], 500);
        }
    }

    /**
     * Remove the specified sale.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $deleted = $this->salesService->deleteSale($id);
            
            if (!$deleted) {
                return $this->sendError(__('sales.not_found'), [], 404);
            }
            
            return $this->sendResponse(null, __('sales.deleted_successfully'));
        } catch (\Exception $e) {
            return $this->sendError(__('sales.deletion_failed'), [], 500);
        }
    }

    /**
     * Get sales statistics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $stats = $this->salesService->getSalesStatistics($request->all());
            
            return $this->sendResponse($stats, __('sales.statistics_retrieved'));
        } catch (\Exception $e) {
            return $this->sendError(__('sales.statistics_failed'), [], 500);
        }
    }
}
