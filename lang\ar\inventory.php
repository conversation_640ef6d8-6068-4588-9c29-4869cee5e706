<?php

return [
    // General Inventory Terms
    'inventory' => 'المخزون',
    'product' => 'المنتج',
    'products' => 'المنتجات',
    'item' => 'عنصر',
    'items' => 'عناصر',
    'stock' => 'المخزون',
    'warehouse' => 'المستودع',
    'warehouses' => 'المستودعات',
    'category' => 'الفئة',
    'categories' => 'الفئات',
    'supplier' => 'المورد',
    'suppliers' => 'الموردون',
    'brand' => 'العلامة التجارية',
    'brands' => 'العلامات التجارية',
    
    // Product Fields
    'name' => 'الاسم',
    'description' => 'الوصف',
    'sku' => 'رمز المنتج',
    'barcode' => 'الباركود',
    'unit_price' => 'سعر الوحدة',
    'cost_price' => 'سعر التكلفة',
    'selling_price' => 'سعر البيع',
    'quantity_in_stock' => 'الكمية في المخزون',
    'available_quantity' => 'الكمية المتاحة',
    'reserved_quantity' => 'الكمية المحجوزة',
    'minimum_stock_level' => 'الحد الأدنى للمخزون',
    'maximum_stock_level' => 'الحد الأقصى للمخزون',
    'reorder_point' => 'نقطة إعادة الطلب',
    'reorder_quantity' => 'كمية إعادة الطلب',
    'unit_of_measure' => 'وحدة القياس',
    'weight' => 'الوزن',
    'dimensions' => 'الأبعاد',
    'location' => 'الموقع',
    'shelf' => 'الرف',
    'bin' => 'الصندوق',
    
    // Stock Status
    'in_stock' => 'متوفر',
    'out_of_stock' => 'غير متوفر',
    'low_stock' => 'مخزون منخفض',
    'overstock' => 'مخزون زائد',
    'discontinued' => 'متوقف',
    'active' => 'نشط',
    'inactive' => 'غير نشط',
    
    // Stock Movements
    'stock_movement' => 'حركة المخزون',
    'stock_movements' => 'حركات المخزون',
    'stock_in' => 'إدخال مخزون',
    'stock_out' => 'إخراج مخزون',
    'stock_adjustment' => 'تعديل المخزون',
    'stock_transfer' => 'نقل المخزون',
    'stock_count' => 'جرد المخزون',
    'movement_type' => 'نوع الحركة',
    'movement_date' => 'تاريخ الحركة',
    'movement_reason' => 'سبب الحركة',
    'old_quantity' => 'الكمية السابقة',
    'new_quantity' => 'الكمية الجديدة',
    'difference' => 'الفرق',
    
    // Actions
    'create_product' => 'إنشاء منتج جديد',
    'edit_product' => 'تعديل المنتج',
    'view_product' => 'عرض المنتج',
    'delete_product' => 'حذف المنتج',
    'duplicate_product' => 'نسخ المنتج',
    'update_stock' => 'تحديث المخزون',
    'add_stock' => 'إضافة مخزون',
    'subtract_stock' => 'خصم مخزون',
    'set_stock' => 'تحديد المخزون',
    'stock_count' => 'جرد المخزون',
    'generate_barcode' => 'إنشاء باركود',
    'print_labels' => 'طباعة الملصقات',
    'export_inventory' => 'تصدير المخزون',
    'import_inventory' => 'استيراد المخزون',
    
    // Messages
    'retrieved_successfully' => 'تم استرداد المخزون بنجاح',
    'created_successfully' => 'تم إنشاء المنتج بنجاح',
    'updated_successfully' => 'تم تحديث المنتج بنجاح',
    'deleted_successfully' => 'تم حذف المنتج بنجاح',
    'not_found' => 'المنتج غير موجود',
    'retrieval_failed' => 'فشل في استرداد المخزون',
    'creation_failed' => 'فشل في إنشاء المنتج',
    'update_failed' => 'فشل في تحديث المنتج',
    'deletion_failed' => 'فشل في حذف المنتج',
    'stock_updated' => 'تم تحديث المخزون بنجاح',
    'stock_update_failed' => 'فشل في تحديث المخزون',
    'low_stock_retrieved' => 'تم استرداد المنتجات منخفضة المخزون',
    'low_stock_failed' => 'فشل في استرداد المنتجات منخفضة المخزون',
    
    // Validation Messages
    'name_required' => 'اسم المنتج مطلوب',
    'name_max' => 'اسم المنتج يجب ألا يتجاوز 255 حرف',
    'description_max' => 'الوصف يجب ألا يتجاوز 1000 حرف',
    'sku_required' => 'رمز المنتج مطلوب',
    'sku_max' => 'رمز المنتج يجب ألا يتجاوز 100 حرف',
    'sku_unique' => 'رمز المنتج موجود مسبقاً',
    'barcode_max' => 'الباركود يجب ألا يتجاوز 100 حرف',
    'barcode_unique' => 'الباركود موجود مسبقاً',
    'category_not_found' => 'الفئة غير موجودة',
    'supplier_not_found' => 'المورد غير موجود',
    'unit_price_required' => 'سعر الوحدة مطلوب',
    'unit_price_numeric' => 'سعر الوحدة يجب أن يكون رقماً',
    'unit_price_min' => 'سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر',
    'cost_price_numeric' => 'سعر التكلفة يجب أن يكون رقماً',
    'cost_price_min' => 'سعر التكلفة يجب أن يكون أكبر من أو يساوي صفر',
    'quantity_integer' => 'الكمية يجب أن تكون رقماً صحيحاً',
    'quantity_min' => 'الكمية يجب أن تكون أكبر من أو تساوي صفر',
    'minimum_stock_integer' => 'الحد الأدنى للمخزون يجب أن يكون رقماً صحيحاً',
    'minimum_stock_min' => 'الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر',
    'maximum_stock_integer' => 'الحد الأقصى للمخزون يجب أن يكون رقماً صحيحاً',
    'maximum_stock_min' => 'الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي صفر',
    'maximum_stock_greater_than_minimum' => 'الحد الأقصى للمخزون يجب أن يكون أكبر من الحد الأدنى',
    'unit_of_measure_max' => 'وحدة القياس يجب ألا تتجاوز 50 حرف',
    'status_invalid' => 'حالة المنتج غير صحيحة',
    
    // Reports
    'inventory_report' => 'تقرير المخزون',
    'stock_valuation' => 'تقييم المخزون',
    'low_stock_report' => 'تقرير المخزون المنخفض',
    'out_of_stock_report' => 'تقرير المخزون المنتهي',
    'stock_movement_report' => 'تقرير حركة المخزون',
    'inventory_aging' => 'تقادم المخزون',
    'abc_analysis' => 'تحليل ABC',
    'inventory_turnover' => 'دوران المخزون',
    'dead_stock' => 'المخزون الراكد',
    'fast_moving' => 'سريع الحركة',
    'slow_moving' => 'بطيء الحركة',
    
    // Dashboard
    'total_products' => 'إجمالي المنتجات',
    'total_value' => 'القيمة الإجمالية',
    'low_stock_count' => 'عدد المنتجات منخفضة المخزون',
    'out_of_stock_count' => 'عدد المنتجات المنتهية',
    'categories_count' => 'عدد الفئات',
    'suppliers_count' => 'عدد الموردين',
    'inventory_value' => 'قيمة المخزون',
    'average_cost' => 'متوسط التكلفة',
];
