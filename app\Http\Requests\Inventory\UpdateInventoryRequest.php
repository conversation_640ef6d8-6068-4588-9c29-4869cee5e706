<?php

namespace App\Http\Requests\Inventory;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateInventoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $productId = $this->route('id') ?? $this->route('product');

        return [
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|max:1000',
            'sku' => [
                'sometimes',
                'string',
                'max:100',
                Rule::unique('products', 'sku')->ignore($productId),
            ],
            'barcode' => [
                'sometimes',
                'string',
                'max:100',
                Rule::unique('products', 'barcode')->ignore($productId),
            ],
            'category_id' => 'sometimes|integer|exists:categories,id',
            'supplier_id' => 'sometimes|integer|exists:suppliers,id',
            'unit_price' => 'sometimes|numeric|min:0',
            'cost_price' => 'sometimes|numeric|min:0',
            'minimum_stock_level' => 'sometimes|integer|min:0',
            'maximum_stock_level' => 'sometimes|integer|min:0',
            'unit_of_measure' => 'sometimes|string|max:50',
            'status' => 'sometimes|in:active,inactive,discontinued',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.max' => __('inventory.name_max'),
            'description.max' => __('inventory.description_max'),
            'sku.max' => __('inventory.sku_max'),
            'sku.unique' => __('inventory.sku_unique'),
            'barcode.max' => __('inventory.barcode_max'),
            'barcode.unique' => __('inventory.barcode_unique'),
            'category_id.exists' => __('inventory.category_not_found'),
            'supplier_id.exists' => __('inventory.supplier_not_found'),
            'unit_price.numeric' => __('inventory.unit_price_numeric'),
            'unit_price.min' => __('inventory.unit_price_min'),
            'cost_price.numeric' => __('inventory.cost_price_numeric'),
            'cost_price.min' => __('inventory.cost_price_min'),
            'minimum_stock_level.integer' => __('inventory.minimum_stock_integer'),
            'minimum_stock_level.min' => __('inventory.minimum_stock_min'),
            'maximum_stock_level.integer' => __('inventory.maximum_stock_integer'),
            'maximum_stock_level.min' => __('inventory.maximum_stock_min'),
            'unit_of_measure.max' => __('inventory.unit_of_measure_max'),
            'status.in' => __('inventory.status_invalid'),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'name' => __('inventory.name'),
            'description' => __('inventory.description'),
            'sku' => __('inventory.sku'),
            'barcode' => __('inventory.barcode'),
            'category_id' => __('inventory.category'),
            'supplier_id' => __('inventory.supplier'),
            'unit_price' => __('inventory.unit_price'),
            'cost_price' => __('inventory.cost_price'),
            'minimum_stock_level' => __('inventory.minimum_stock_level'),
            'maximum_stock_level' => __('inventory.maximum_stock_level'),
            'unit_of_measure' => __('inventory.unit_of_measure'),
            'status' => __('inventory.status'),
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate that maximum stock level is greater than minimum stock level
            if ($this->filled('minimum_stock_level') && $this->filled('maximum_stock_level')) {
                if ($this->input('maximum_stock_level') <= $this->input('minimum_stock_level')) {
                    $validator->errors()->add('maximum_stock_level', __('inventory.maximum_stock_greater_than_minimum'));
                }
            }
        });
    }
}
