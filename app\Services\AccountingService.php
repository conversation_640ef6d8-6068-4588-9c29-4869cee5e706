<?php

namespace App\Services;

use App\Models\Accounting\Transaction;
use App\Models\Accounting\Account;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class AccountingService
{
    /**
     * Get all transactions with pagination.
     *
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllTransactions(int $perPage = 15): LengthAwarePaginator
    {
        return Transaction::with(['account', 'category'])
            ->orderBy('transaction_date', 'desc')
            ->paginate($perPage);
    }

    /**
     * Create a new transaction.
     *
     * @param array $data
     * @return Transaction
     * @throws \Exception
     */
    public function createTransaction(array $data): Transaction
    {
        DB::beginTransaction();
        
        try {
            $transaction = Transaction::create([
                'account_id' => $data['account_id'],
                'category_id' => $data['category_id'] ?? null,
                'type' => $data['type'], // income, expense, transfer
                'amount' => $data['amount'],
                'description' => $data['description'],
                'transaction_date' => $data['transaction_date'] ?? now(),
                'reference_number' => $data['reference_number'] ?? null,
                'status' => $data['status'] ?? 'completed',
            ]);

            // Update account balance
            $this->updateAccountBalance($data['account_id'], $data['amount'], $data['type']);

            DB::commit();
            
            return $transaction->load(['account', 'category']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get transaction by ID.
     *
     * @param int $id
     * @return Transaction|null
     */
    public function getTransactionById(int $id): ?Transaction
    {
        return Transaction::with(['account', 'category'])->find($id);
    }

    /**
     * Update transaction.
     *
     * @param int $id
     * @param array $data
     * @return Transaction|null
     * @throws \Exception
     */
    public function updateTransaction(int $id, array $data): ?Transaction
    {
        $transaction = Transaction::find($id);
        
        if (!$transaction) {
            return null;
        }

        DB::beginTransaction();
        
        try {
            // Reverse the old transaction effect on account balance
            $this->updateAccountBalance(
                $transaction->account_id, 
                $transaction->amount, 
                $this->getOppositeType($transaction->type)
            );

            $transaction->update([
                'account_id' => $data['account_id'] ?? $transaction->account_id,
                'category_id' => $data['category_id'] ?? $transaction->category_id,
                'type' => $data['type'] ?? $transaction->type,
                'amount' => $data['amount'] ?? $transaction->amount,
                'description' => $data['description'] ?? $transaction->description,
                'transaction_date' => $data['transaction_date'] ?? $transaction->transaction_date,
                'reference_number' => $data['reference_number'] ?? $transaction->reference_number,
                'status' => $data['status'] ?? $transaction->status,
            ]);

            // Apply the new transaction effect on account balance
            $this->updateAccountBalance(
                $transaction->account_id, 
                $transaction->amount, 
                $transaction->type
            );

            DB::commit();
            
            return $transaction->load(['account', 'category']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete transaction.
     *
     * @param int $id
     * @return bool
     */
    public function deleteTransaction(int $id): bool
    {
        $transaction = Transaction::find($id);
        
        if (!$transaction) {
            return false;
        }

        DB::beginTransaction();
        
        try {
            // Reverse the transaction effect on account balance
            $this->updateAccountBalance(
                $transaction->account_id, 
                $transaction->amount, 
                $this->getOppositeType($transaction->type)
            );

            $transaction->delete();
            
            DB::commit();
            
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Generate financial report.
     *
     * @param string $type
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public function generateReport(string $type, string $startDate, string $endDate): array
    {
        switch ($type) {
            case 'income_statement':
                return $this->generateIncomeStatement($startDate, $endDate);
            case 'balance_sheet':
                return $this->generateBalanceSheet($endDate);
            case 'cash_flow':
                return $this->generateCashFlowStatement($startDate, $endDate);
            default:
                throw new \InvalidArgumentException('Invalid report type');
        }
    }

    /**
     * Get account balances.
     *
     * @return Collection
     */
    public function getAccountBalances(): Collection
    {
        return Account::with(['transactions' => function ($query) {
            $query->where('status', 'completed');
        }])
        ->orderBy('name')
        ->get()
        ->map(function ($account) {
            return [
                'id' => $account->id,
                'name' => $account->name,
                'type' => $account->type,
                'balance' => $account->balance,
                'currency' => $account->currency ?? 'USD',
            ];
        });
    }

    /**
     * Update account balance.
     *
     * @param int $accountId
     * @param float $amount
     * @param string $type
     * @return void
     */
    private function updateAccountBalance(int $accountId, float $amount, string $type): void
    {
        $account = Account::find($accountId);
        
        if (!$account) {
            throw new \Exception('Account not found');
        }

        switch ($type) {
            case 'income':
                $account->increment('balance', $amount);
                break;
            case 'expense':
                $account->decrement('balance', $amount);
                break;
            case 'transfer':
                // Handle transfer logic separately
                break;
        }
    }

    /**
     * Get opposite transaction type.
     *
     * @param string $type
     * @return string
     */
    private function getOppositeType(string $type): string
    {
        return match ($type) {
            'income' => 'expense',
            'expense' => 'income',
            default => $type,
        };
    }

    /**
     * Generate income statement.
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    private function generateIncomeStatement(string $startDate, string $endDate): array
    {
        $income = Transaction::where('type', 'income')
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->sum('amount');

        $expenses = Transaction::where('type', 'expense')
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->sum('amount');

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'total_income' => $income,
            'total_expenses' => $expenses,
            'net_income' => $income - $expenses,
        ];
    }

    /**
     * Generate balance sheet.
     *
     * @param string $asOfDate
     * @return array
     */
    private function generateBalanceSheet(string $asOfDate): array
    {
        $accounts = Account::with(['transactions' => function ($query) use ($asOfDate) {
            $query->where('transaction_date', '<=', $asOfDate)
                  ->where('status', 'completed');
        }])->get();

        $assets = $accounts->where('type', 'asset')->sum('balance');
        $liabilities = $accounts->where('type', 'liability')->sum('balance');
        $equity = $accounts->where('type', 'equity')->sum('balance');

        return [
            'as_of_date' => $asOfDate,
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => $equity,
            'total_liabilities_equity' => $liabilities + $equity,
        ];
    }

    /**
     * Generate cash flow statement.
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    private function generateCashFlowStatement(string $startDate, string $endDate): array
    {
        $cashInflows = Transaction::where('type', 'income')
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->sum('amount');

        $cashOutflows = Transaction::where('type', 'expense')
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->sum('amount');

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'cash_inflows' => $cashInflows,
            'cash_outflows' => $cashOutflows,
            'net_cash_flow' => $cashInflows - $cashOutflows,
        ];
    }
}
