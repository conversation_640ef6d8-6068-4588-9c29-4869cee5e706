<?php

namespace App\Services;

use App\Models\Sales\Sale;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class SalesService
{
    /**
     * Get all sales with pagination.
     *
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllSales(int $perPage = 15): LengthAwarePaginator
    {
        return Sale::with(['customer', 'items'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Create a new sale.
     *
     * @param array $data
     * @return Sale
     * @throws \Exception
     */
    public function createSale(array $data): Sale
    {
        DB::beginTransaction();
        
        try {
            $sale = Sale::create([
                'customer_id' => $data['customer_id'],
                'sale_date' => $data['sale_date'] ?? now(),
                'total_amount' => $data['total_amount'],
                'tax_amount' => $data['tax_amount'] ?? 0,
                'discount_amount' => $data['discount_amount'] ?? 0,
                'status' => $data['status'] ?? 'pending',
                'notes' => $data['notes'] ?? null,
            ]);

            // Add sale items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                foreach ($data['items'] as $item) {
                    $sale->items()->create([
                        'product_id' => $item['product_id'],
                        'quantity' => $item['quantity'],
                        'unit_price' => $item['unit_price'],
                        'total_price' => $item['quantity'] * $item['unit_price'],
                    ]);
                }
            }

            DB::commit();
            
            return $sale->load(['customer', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get sale by ID.
     *
     * @param int $id
     * @return Sale|null
     */
    public function getSaleById(int $id): ?Sale
    {
        return Sale::with(['customer', 'items.product'])->find($id);
    }

    /**
     * Update sale.
     *
     * @param int $id
     * @param array $data
     * @return Sale|null
     * @throws \Exception
     */
    public function updateSale(int $id, array $data): ?Sale
    {
        $sale = Sale::find($id);
        
        if (!$sale) {
            return null;
        }

        DB::beginTransaction();
        
        try {
            $sale->update([
                'customer_id' => $data['customer_id'] ?? $sale->customer_id,
                'sale_date' => $data['sale_date'] ?? $sale->sale_date,
                'total_amount' => $data['total_amount'] ?? $sale->total_amount,
                'tax_amount' => $data['tax_amount'] ?? $sale->tax_amount,
                'discount_amount' => $data['discount_amount'] ?? $sale->discount_amount,
                'status' => $data['status'] ?? $sale->status,
                'notes' => $data['notes'] ?? $sale->notes,
            ]);

            // Update sale items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                $sale->items()->delete();
                
                foreach ($data['items'] as $item) {
                    $sale->items()->create([
                        'product_id' => $item['product_id'],
                        'quantity' => $item['quantity'],
                        'unit_price' => $item['unit_price'],
                        'total_price' => $item['quantity'] * $item['unit_price'],
                    ]);
                }
            }

            DB::commit();
            
            return $sale->load(['customer', 'items']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete sale.
     *
     * @param int $id
     * @return bool
     */
    public function deleteSale(int $id): bool
    {
        $sale = Sale::find($id);
        
        if (!$sale) {
            return false;
        }

        DB::beginTransaction();
        
        try {
            $sale->items()->delete();
            $sale->delete();
            
            DB::commit();
            
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get sales statistics.
     *
     * @param array $filters
     * @return array
     */
    public function getSalesStatistics(array $filters = []): array
    {
        $query = Sale::query();

        // Apply date filters
        if (isset($filters['start_date'])) {
            $query->whereDate('sale_date', '>=', $filters['start_date']);
        }
        
        if (isset($filters['end_date'])) {
            $query->whereDate('sale_date', '<=', $filters['end_date']);
        }

        $totalSales = $query->sum('total_amount');
        $totalCount = $query->count();
        $averageSale = $totalCount > 0 ? $totalSales / $totalCount : 0;

        return [
            'total_sales' => $totalSales,
            'total_count' => $totalCount,
            'average_sale' => round($averageSale, 2),
            'monthly_sales' => $this->getMonthlySales($filters),
            'top_customers' => $this->getTopCustomers($filters),
        ];
    }

    /**
     * Get monthly sales data.
     *
     * @param array $filters
     * @return Collection
     */
    private function getMonthlySales(array $filters = []): Collection
    {
        $query = Sale::selectRaw('YEAR(sale_date) as year, MONTH(sale_date) as month, SUM(total_amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc');

        if (isset($filters['start_date'])) {
            $query->whereDate('sale_date', '>=', $filters['start_date']);
        }
        
        if (isset($filters['end_date'])) {
            $query->whereDate('sale_date', '<=', $filters['end_date']);
        }

        return $query->get();
    }

    /**
     * Get top customers by sales.
     *
     * @param array $filters
     * @return Collection
     */
    private function getTopCustomers(array $filters = []): Collection
    {
        $query = Sale::with('customer')
            ->selectRaw('customer_id, SUM(total_amount) as total_sales, COUNT(*) as total_orders')
            ->groupBy('customer_id')
            ->orderBy('total_sales', 'desc')
            ->limit(10);

        if (isset($filters['start_date'])) {
            $query->whereDate('sale_date', '>=', $filters['start_date']);
        }
        
        if (isset($filters['end_date'])) {
            $query->whereDate('sale_date', '<=', $filters['end_date']);
        }

        return $query->get();
    }
}
