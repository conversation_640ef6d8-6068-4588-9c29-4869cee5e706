<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| API Health Check
|--------------------------------------------------------------------------
*/

Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0'
    ]);
});

/*
|--------------------------------------------------------------------------
| API Information
|--------------------------------------------------------------------------
*/

Route::get('/info', function () {
    return response()->json([
        'name' => 'OmniFlow ERP API',
        'version' => '1.0.0',
        'description' => 'Laravel ERP System API',
        'modules' => [
            'auth' => 'Authentication & Authorization',
            'sales' => 'Sales Management',
            'inventory' => 'Inventory Management',
            'accounting' => 'Accounting & Finance'
        ]
    ]);
});
