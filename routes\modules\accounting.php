<?php

use App\Http\Controllers\API\V1\Accounting\AccountingController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Accounting API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the Accounting module. These routes are loaded
| by the RouteServiceProvider and are assigned the "api" middleware group.
|
*/

Route::prefix('api/v1')->middleware(['api', 'auth:sanctum'])->group(function () {
    
    // Accounting Routes
    Route::prefix('accounting')->name('accounting.')->group(function () {
        
        // Transaction CRUD Operations
        Route::prefix('transactions')->name('transactions.')->group(function () {
            Route::get('/', [AccountingController::class, 'index'])->name('index');
            Route::post('/', [AccountingController::class, 'store'])->name('store');
            Route::get('/{id}', [AccountingController::class, 'show'])->name('show');
            Route::put('/{id}', [AccountingController::class, 'update'])->name('update');
            Route::delete('/{id}', [AccountingController::class, 'destroy'])->name('destroy');
        });
        
        // Financial Reports
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/', [AccountingController::class, 'reports'])->name('generate');
        });
        
        // Account Balances
        Route::get('/balances', [AccountingController::class, 'balances'])->name('balances');
        
    });
    
    // Alternative shorter routes for transactions
    Route::prefix('transactions')->name('transactions.')->group(function () {
        
        Route::get('/', [AccountingController::class, 'index'])->name('api.index');
        Route::post('/', [AccountingController::class, 'store'])->name('api.store');
        Route::get('/{id}', [AccountingController::class, 'show'])->name('api.show');
        Route::put('/{id}', [AccountingController::class, 'update'])->name('api.update');
        Route::delete('/{id}', [AccountingController::class, 'destroy'])->name('api.destroy');
        
    });
    
});

/*
|--------------------------------------------------------------------------
| Accounting Web Routes (if needed)
|--------------------------------------------------------------------------
*/

Route::prefix('accounting')->middleware(['web', 'auth'])->name('accounting.')->group(function () {
    
    // Web routes for accounting module can be added here
    // Example: Route::get('/', [WebAccountingController::class, 'index'])->name('web.index');
    
});
