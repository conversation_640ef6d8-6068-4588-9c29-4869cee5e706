<?php

use App\Http\Controllers\API\V1\Auth\AuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Authentication API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for authentication. These routes are loaded
| by the RouteServiceProvider and are assigned the "api" middleware group.
|
*/

Route::prefix('api/v1')->middleware(['api'])->group(function () {
    
    // Public Authentication Routes (no auth required)
    Route::prefix('auth')->name('auth.')->group(function () {
        
        Route::post('/register', [AuthController::class, 'register'])->name('register');
        Route::post('/login', [AuthController::class, 'login'])->name('login');
        
    });
    
    // Protected Authentication Routes (auth required)
    Route::prefix('auth')->middleware(['auth:sanctum'])->name('auth.')->group(function () {
        
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('/user', [AuthController::class, 'user'])->name('user');
        Route::post('/refresh', [AuthController::class, 'refresh'])->name('refresh');
        
    });
    
});

/*
|--------------------------------------------------------------------------
| Alternative Auth Routes (shorter paths)
|--------------------------------------------------------------------------
*/

Route::prefix('api/v1')->middleware(['api'])->group(function () {
    
    // Public routes
    Route::post('/register', [AuthController::class, 'register'])->name('api.register');
    Route::post('/login', [AuthController::class, 'login'])->name('api.login');
    
    // Protected routes
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::post('/logout', [AuthController::class, 'logout'])->name('api.logout');
        Route::get('/me', [AuthController::class, 'user'])->name('api.me');
        Route::post('/refresh-token', [AuthController::class, 'refresh'])->name('api.refresh');
    });
    
});
