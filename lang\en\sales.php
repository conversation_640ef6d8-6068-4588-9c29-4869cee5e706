<?php

return [
    // General Sales Terms
    'sales' => 'Sales',
    'sale' => 'Sale',
    'customer' => 'Customer',
    'customers' => 'Customers',
    'product' => 'Product',
    'products' => 'Products',
    'item' => 'Item',
    'items' => 'Items',
    'order' => 'Order',
    'orders' => 'Orders',
    'invoice' => 'Invoice',
    'invoices' => 'Invoices',
    'receipt' => 'Receipt',
    'receipts' => 'Receipts',
    
    // Sale Fields
    'sale_date' => 'Sale Date',
    'customer_name' => 'Customer Name',
    'customer_email' => 'Customer Email',
    'customer_phone' => 'Customer Phone',
    'total_amount' => 'Total Amount',
    'tax_amount' => 'Tax Amount',
    'discount_amount' => 'Discount Amount',
    'net_amount' => 'Net Amount',
    'unit_price' => 'Unit Price',
    'quantity' => 'Quantity',
    'line_total' => 'Line Total',
    'payment_method' => 'Payment Method',
    'payment_status' => 'Payment Status',
    'delivery_address' => 'Delivery Address',
    'delivery_date' => 'Delivery Date',
    'sales_person' => 'Sales Person',
    
    // Sale Status
    'pending' => 'Pending',
    'confirmed' => 'Confirmed',
    'shipped' => 'Shipped',
    'delivered' => 'Delivered',
    'cancelled' => 'Cancelled',
    'returned' => 'Returned',
    'refunded' => 'Refunded',
    
    // Payment Status
    'unpaid' => 'Unpaid',
    'partially_paid' => 'Partially Paid',
    'paid' => 'Paid',
    'overdue' => 'Overdue',
    
    // Payment Methods
    'cash' => 'Cash',
    'credit_card' => 'Credit Card',
    'debit_card' => 'Debit Card',
    'bank_transfer' => 'Bank Transfer',
    'check' => 'Check',
    'online_payment' => 'Online Payment',
    
    // Messages
    'retrieved_successfully' => 'Sales retrieved successfully',
    'created_successfully' => 'Sale created successfully',
    'updated_successfully' => 'Sale updated successfully',
    'deleted_successfully' => 'Sale deleted successfully',
    'not_found' => 'Sale not found',
    'retrieval_failed' => 'Failed to retrieve sales',
    'creation_failed' => 'Failed to create sale',
    'update_failed' => 'Failed to update sale',
    'deletion_failed' => 'Failed to delete sale',
    'statistics_retrieved' => 'Sales statistics retrieved',
    'statistics_failed' => 'Failed to retrieve statistics',
    
    // Validation Messages
    'customer_required' => 'Customer is required',
    'customer_not_found' => 'Customer not found',
    'total_amount_required' => 'Total amount is required',
    'total_amount_numeric' => 'Total amount must be a number',
    'total_amount_min' => 'Total amount must be greater than or equal to zero',
    'tax_amount_numeric' => 'Tax amount must be a number',
    'tax_amount_min' => 'Tax amount must be greater than or equal to zero',
    'discount_amount_numeric' => 'Discount amount must be a number',
    'discount_amount_min' => 'Discount amount must be greater than or equal to zero',
    'status_invalid' => 'Invalid sale status',
    'notes_max' => 'Notes must not exceed 1000 characters',
    
    // Items Validation
    'items_array' => 'Items must be an array',
    'item_product_required' => 'Product is required for item',
    'item_product_not_found' => 'Product not found',
    'item_quantity_required' => 'Quantity is required for item',
    'item_quantity_integer' => 'Quantity must be an integer',
    'item_quantity_min' => 'Quantity must be greater than zero',
    'item_price_required' => 'Price is required for item',
    'item_price_numeric' => 'Price must be a number',
    'item_price_min' => 'Price must be greater than or equal to zero',
    
    // Reports
    'sales_report' => 'Sales Report',
    'daily_sales' => 'Daily Sales',
    'weekly_sales' => 'Weekly Sales',
    'monthly_sales' => 'Monthly Sales',
    'yearly_sales' => 'Yearly Sales',
    'top_customers' => 'Top Customers',
    'top_products' => 'Top Products',
    'sales_by_category' => 'Sales by Category',
    'sales_by_region' => 'Sales by Region',
    'sales_trend' => 'Sales Trend',
    'average_order_value' => 'Average Order Value',
    'conversion_rate' => 'Conversion Rate',
    
    // Dashboard
    'total_sales' => 'Total Sales',
    'sales_count' => 'Sales Count',
    'average_sale' => 'Average Sale',
    'sales_growth' => 'Sales Growth',
    'revenue' => 'Revenue',
    'profit' => 'Profit',
    'margin' => 'Margin',
];
